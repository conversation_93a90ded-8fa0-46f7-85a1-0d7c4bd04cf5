[2025-06-19 02:03:04] local.ERROR: Method Illuminate\Http\Request::validated does not exist. {"userId":2,"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Http\\Request::validated does not exist. at /var/www/vendor/laravel/framework/src/Illuminate/Macroable/Traits/Macroable.php:115)
[stacktrace]
#0 /var/www/app/Http/Controllers/Api/V1/InvitationController.php(73): Illuminate\\Http\\Request->__call('validated', Array)
#1 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\Api\\V1\\InvitationController->store(Object(Illuminate\\Http\\Request))
#2 /var/www/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#3 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\InvitationController), 'store')
#4 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#5 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#6 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#7 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authorize.php(59): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'create', 'App\\\\Models\\\\Invi...')
#9 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#10 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#13 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}(Object(Illuminate\\Http\\Request))
#15 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /var/www/app/Http/Middleware/ApiMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): App\\Http\\Middleware\\ApiMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#27 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#28 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#40 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 /var/www/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}
"} 
[2025-06-19 09:29:40] local.ERROR: Method Illuminate\Http\Request::validated does not exist. {"userId":2,"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Http\\Request::validated does not exist. at /var/www/vendor/laravel/framework/src/Illuminate/Macroable/Traits/Macroable.php:115)
[stacktrace]
#0 /var/www/app/Http/Controllers/Api/V1/InvitationController.php(73): Illuminate\\Http\\Request->__call('validated', Array)
#1 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\Api\\V1\\InvitationController->store(Object(Illuminate\\Http\\Request))
#2 /var/www/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#3 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\InvitationController), 'store')
#4 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#5 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#6 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#7 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authorize.php(59): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'create', 'App\\\\Models\\\\Invi...')
#9 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#10 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /var/www/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#13 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}(Object(Illuminate\\Http\\Request))
#15 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 /var/www/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /var/www/app/Http/Middleware/ApiMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#19 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): App\\Http\\Middleware\\ApiMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 /var/www/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#27 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#28 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#40 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /var/www/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /var/www/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 /var/www/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 /var/www/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#50 {main}
"} 
[2025-06-20 02:40:20] local.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 068947. This code will expire in 15 minutes."} 
[2025-06-20 02:40:20] local.INFO: Verification code sent {"email":"<EMAIL>","code":"068947","expires_at":"2025-06-20T02:55:20.343545Z"} 
[2025-06-20 03:30:20] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:30:38] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:30:59] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:31:28] local.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 03:32:11] local.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 768655. This code will expire in 15 minutes."} 
[2025-06-20 03:32:11] local.INFO: Verification code sent {"email":"<EMAIL>","code":"768655","expires_at":"2025-06-20T03:47:11.682324Z"} 
[2025-06-20 03:32:33] local.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 03:34:19] local.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 035764. This code will expire in 15 minutes."} 
[2025-06-20 03:34:19] local.INFO: Verification code sent {"email":"<EMAIL>","code":"035764","expires_at":"2025-06-20T03:49:19.467621Z"} 
[2025-06-20 03:35:36] local.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 604224. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"604224","expires_at":"2025-06-20T11:17:47.489567Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 003360. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"003360","expires_at":"2025-06-20T11:17:47.495960Z"} 
[2025-06-20 11:02:47] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 209262. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"209262","expires_at":"2025-06-20T11:17:47.501926Z"} 
[2025-06-20 11:02:47] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 594243. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"594243","expires_at":"2025-06-20T11:17:47.517656Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 654736. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"654736","expires_at":"2025-06-20T11:17:47.517878Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 876324. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"876324","expires_at":"2025-06-20T11:17:47.528711Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 109707. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"109707","expires_at":"2025-06-20T11:17:47.534277Z"} 
[2025-06-20 11:02:47] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 493893. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"493893","expires_at":"2025-06-20T11:17:47.540801Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 050385. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"050385","expires_at":"2025-06-20T11:17:47.541015Z"} 
[2025-06-20 11:02:47] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 936186. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"936186","expires_at":"2025-06-20T11:17:47.546341Z"} 
[2025-06-20 11:02:47] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 097978. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"097978","expires_at":"2025-06-20T11:17:47.551905Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 942018. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"942018","expires_at":"2025-06-20T11:17:47.553553Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 809880. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"809880","expires_at":"2025-06-20T11:17:47.553773Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 897856. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"897856","expires_at":"2025-06-20T11:17:47.553975Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 688649. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"688649","expires_at":"2025-06-20T11:17:47.554191Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 580921. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"580921","expires_at":"2025-06-20T11:17:47.554385Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 213113. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"213113","expires_at":"2025-06-20T11:17:47.554599Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 567907. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"567907","expires_at":"2025-06-20T11:17:47.554811Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 441503. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"441503","expires_at":"2025-06-20T11:17:47.555003Z"} 
[2025-06-20 11:02:47] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 865627. This code will expire in 15 minutes."} 
[2025-06-20 11:02:47] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"865627","expires_at":"2025-06-20T11:17:47.555207Z"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 933573. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"933573","expires_at":"2025-06-20T11:17:49.248946Z"} 
[2025-06-20 11:02:49] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:49] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 966616. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"966616","expires_at":"2025-06-20T11:17:49.283702Z"} 
[2025-06-20 11:02:49] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 347788. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"347788","expires_at":"2025-06-20T11:17:49.476722Z"} 
[2025-06-20 11:02:49] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 148825. This code will expire in 15 minutes."} 
[2025-06-20 11:02:49] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"148825","expires_at":"2025-06-20T11:17:49.506361Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 467798. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"467798","expires_at":"2025-06-20T12:32:57.402332Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 173611. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"173611","expires_at":"2025-06-20T12:32:57.408913Z"} 
[2025-06-20 12:17:57] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 372269. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"372269","expires_at":"2025-06-20T12:32:57.415028Z"} 
[2025-06-20 12:17:57] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 842211. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"842211","expires_at":"2025-06-20T12:32:57.430570Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 492479. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"492479","expires_at":"2025-06-20T12:32:57.430797Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 980807. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"980807","expires_at":"2025-06-20T12:32:57.441519Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 553903. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"553903","expires_at":"2025-06-20T12:32:57.447168Z"} 
[2025-06-20 12:17:57] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 044549. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"044549","expires_at":"2025-06-20T12:32:57.454047Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 725455. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"725455","expires_at":"2025-06-20T12:32:57.454297Z"} 
[2025-06-20 12:17:57] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 110979. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"110979","expires_at":"2025-06-20T12:32:57.459882Z"} 
[2025-06-20 12:17:57] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 396491. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"396491","expires_at":"2025-06-20T12:32:57.465415Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 610733. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"610733","expires_at":"2025-06-20T12:32:57.467031Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 986306. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"986306","expires_at":"2025-06-20T12:32:57.467268Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 606409. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"606409","expires_at":"2025-06-20T12:32:57.467487Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 898065. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"898065","expires_at":"2025-06-20T12:32:57.467691Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 937669. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"937669","expires_at":"2025-06-20T12:32:57.467891Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 093162. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"093162","expires_at":"2025-06-20T12:32:57.468088Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 852863. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"852863","expires_at":"2025-06-20T12:32:57.468283Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 446710. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"446710","expires_at":"2025-06-20T12:32:57.468487Z"} 
[2025-06-20 12:17:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 009016. This code will expire in 15 minutes."} 
[2025-06-20 12:17:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"009016","expires_at":"2025-06-20T12:32:57.468690Z"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 625740. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"625740","expires_at":"2025-06-20T12:32:59.362664Z"} 
[2025-06-20 12:17:59] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:59] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 621805. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"621805","expires_at":"2025-06-20T12:32:59.406300Z"} 
[2025-06-20 12:17:59] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 338343. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"338343","expires_at":"2025-06-20T12:32:59.606075Z"} 
[2025-06-20 12:17:59] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 921991. This code will expire in 15 minutes."} 
[2025-06-20 12:17:59] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"921991","expires_at":"2025-06-20T12:32:59.646725Z"} 
[2025-06-20 12:35:53] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 084801. This code will expire in 15 minutes."} 
[2025-06-20 12:35:53] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"084801","expires_at":"2025-06-20T12:50:53.986617Z"} 
[2025-06-20 12:35:53] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 370524. This code will expire in 15 minutes."} 
[2025-06-20 12:35:53] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"370524","expires_at":"2025-06-20T12:50:53.993210Z"} 
[2025-06-20 12:35:53] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:53] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 759197. This code will expire in 15 minutes."} 
[2025-06-20 12:35:53] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"759197","expires_at":"2025-06-20T12:50:53.999341Z"} 
[2025-06-20 12:35:53] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 109734. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"109734","expires_at":"2025-06-20T12:50:54.015241Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 095277. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"095277","expires_at":"2025-06-20T12:50:54.015473Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 068300. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"068300","expires_at":"2025-06-20T12:50:54.026378Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 325662. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"325662","expires_at":"2025-06-20T12:50:54.031510Z"} 
[2025-06-20 12:35:54] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 680757. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"680757","expires_at":"2025-06-20T12:50:54.038043Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 562255. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"562255","expires_at":"2025-06-20T12:50:54.038279Z"} 
[2025-06-20 12:35:54] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 206916. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"206916","expires_at":"2025-06-20T12:50:54.043882Z"} 
[2025-06-20 12:35:54] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 872381. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"872381","expires_at":"2025-06-20T12:50:54.049288Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 751367. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"751367","expires_at":"2025-06-20T12:50:54.050846Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 294470. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"294470","expires_at":"2025-06-20T12:50:54.051077Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 141378. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"141378","expires_at":"2025-06-20T12:50:54.051281Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 125336. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"125336","expires_at":"2025-06-20T12:50:54.051478Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 352917. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"352917","expires_at":"2025-06-20T12:50:54.051685Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 689280. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"689280","expires_at":"2025-06-20T12:50:54.051919Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 796846. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"796846","expires_at":"2025-06-20T12:50:54.052115Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 141548. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"141548","expires_at":"2025-06-20T12:50:54.052313Z"} 
[2025-06-20 12:35:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 896385. This code will expire in 15 minutes."} 
[2025-06-20 12:35:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"896385","expires_at":"2025-06-20T12:50:54.052518Z"} 
[2025-06-20 12:35:55] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 185668. This code will expire in 15 minutes."} 
[2025-06-20 12:35:55] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"185668","expires_at":"2025-06-20T12:50:55.909484Z"} 
[2025-06-20 12:35:55] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:55] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:35:55] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 864592. This code will expire in 15 minutes."} 
[2025-06-20 12:35:55] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"864592","expires_at":"2025-06-20T12:50:55.960346Z"} 
[2025-06-20 12:35:55] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:35:56] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 038346. This code will expire in 15 minutes."} 
[2025-06-20 12:35:56] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"038346","expires_at":"2025-06-20T12:50:56.159125Z"} 
[2025-06-20 12:35:56] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 116661. This code will expire in 15 minutes."} 
[2025-06-20 12:35:56] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"116661","expires_at":"2025-06-20T12:50:56.196985Z"} 
[2025-06-20 12:37:42] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 986807. This code will expire in 15 minutes."} 
[2025-06-20 12:37:42] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"986807","expires_at":"2025-06-20T12:52:42.995421Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 177586. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"177586","expires_at":"2025-06-20T12:52:43.002441Z"} 
[2025-06-20 12:37:43] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 907367. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"907367","expires_at":"2025-06-20T12:52:43.008607Z"} 
[2025-06-20 12:37:43] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:37:43] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 969168. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"969168","expires_at":"2025-06-20T12:52:43.024495Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 230382. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"230382","expires_at":"2025-06-20T12:52:43.024722Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 271765. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"271765","expires_at":"2025-06-20T12:52:43.036445Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 119331. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"119331","expires_at":"2025-06-20T12:52:43.042066Z"} 
[2025-06-20 12:37:43] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 305729. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"305729","expires_at":"2025-06-20T12:52:43.049091Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 829240. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"829240","expires_at":"2025-06-20T12:52:43.049312Z"} 
[2025-06-20 12:37:43] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:37:43] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 601972. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"601972","expires_at":"2025-06-20T12:52:43.055255Z"} 
[2025-06-20 12:37:43] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 813742. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"813742","expires_at":"2025-06-20T12:52:43.061368Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 270996. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"270996","expires_at":"2025-06-20T12:52:43.063055Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 395961. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"395961","expires_at":"2025-06-20T12:52:43.063348Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 226676. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"226676","expires_at":"2025-06-20T12:52:43.063605Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 894134. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"894134","expires_at":"2025-06-20T12:52:43.063837Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 303038. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"303038","expires_at":"2025-06-20T12:52:43.064032Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 759320. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"759320","expires_at":"2025-06-20T12:52:43.064227Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 738441. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"738441","expires_at":"2025-06-20T12:52:43.064419Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 305521. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"305521","expires_at":"2025-06-20T12:52:43.064637Z"} 
[2025-06-20 12:37:43] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 642769. This code will expire in 15 minutes."} 
[2025-06-20 12:37:43] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"642769","expires_at":"2025-06-20T12:52:43.064833Z"} 
[2025-06-20 12:37:45] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 351779. This code will expire in 15 minutes."} 
[2025-06-20 12:37:45] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"351779","expires_at":"2025-06-20T12:52:45.027499Z"} 
[2025-06-20 12:37:45] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:37:45] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:37:45] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 093360. This code will expire in 15 minutes."} 
[2025-06-20 12:37:45] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"093360","expires_at":"2025-06-20T12:52:45.081014Z"} 
[2025-06-20 12:37:45] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:37:45] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 381553. This code will expire in 15 minutes."} 
[2025-06-20 12:37:45] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"381553","expires_at":"2025-06-20T12:52:45.290715Z"} 
[2025-06-20 12:37:45] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 928478. This code will expire in 15 minutes."} 
[2025-06-20 12:37:45] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"928478","expires_at":"2025-06-20T12:52:45.333129Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 164556. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"164556","expires_at":"2025-06-20T12:53:54.842327Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 359339. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"359339","expires_at":"2025-06-20T12:53:54.848409Z"} 
[2025-06-20 12:38:54] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 161296. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"161296","expires_at":"2025-06-20T12:53:54.854339Z"} 
[2025-06-20 12:38:54] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:38:54] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 978844. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"978844","expires_at":"2025-06-20T12:53:54.871200Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 268361. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"268361","expires_at":"2025-06-20T12:53:54.871458Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 200260. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"200260","expires_at":"2025-06-20T12:53:54.882689Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 770649. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"770649","expires_at":"2025-06-20T12:53:54.888429Z"} 
[2025-06-20 12:38:54] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 792384. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"792384","expires_at":"2025-06-20T12:53:54.895720Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 584742. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"584742","expires_at":"2025-06-20T12:53:54.895958Z"} 
[2025-06-20 12:38:54] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:38:54] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 686567. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"686567","expires_at":"2025-06-20T12:53:54.901566Z"} 
[2025-06-20 12:38:54] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 912567. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"912567","expires_at":"2025-06-20T12:53:54.907130Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 811075. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"811075","expires_at":"2025-06-20T12:53:54.908922Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 971474. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"971474","expires_at":"2025-06-20T12:53:54.909163Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 515562. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"515562","expires_at":"2025-06-20T12:53:54.909371Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 693438. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"693438","expires_at":"2025-06-20T12:53:54.909568Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 535650. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"535650","expires_at":"2025-06-20T12:53:54.909780Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 021249. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"021249","expires_at":"2025-06-20T12:53:54.909973Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 217193. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"217193","expires_at":"2025-06-20T12:53:54.910204Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 913944. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"913944","expires_at":"2025-06-20T12:53:54.910398Z"} 
[2025-06-20 12:38:54] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 099851. This code will expire in 15 minutes."} 
[2025-06-20 12:38:54] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"099851","expires_at":"2025-06-20T12:53:54.910594Z"} 
[2025-06-20 12:38:56] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 768850. This code will expire in 15 minutes."} 
[2025-06-20 12:38:56] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"768850","expires_at":"2025-06-20T12:53:56.977826Z"} 
[2025-06-20 12:38:56] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:38:56] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:38:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 967006. This code will expire in 15 minutes."} 
[2025-06-20 12:38:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"967006","expires_at":"2025-06-20T12:53:57.023072Z"} 
[2025-06-20 12:38:57] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:38:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 222239. This code will expire in 15 minutes."} 
[2025-06-20 12:38:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"222239","expires_at":"2025-06-20T12:53:57.213622Z"} 
[2025-06-20 12:38:57] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 893719. This code will expire in 15 minutes."} 
[2025-06-20 12:38:57] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"893719","expires_at":"2025-06-20T12:53:57.253291Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 868307. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"868307","expires_at":"2025-06-20T12:54:09.832655Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 032155. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"032155","expires_at":"2025-06-20T12:54:09.838351Z"} 
[2025-06-20 12:39:09] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 667457. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"667457","expires_at":"2025-06-20T12:54:09.843540Z"} 
[2025-06-20 12:39:09] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:39:09] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 789874. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"789874","expires_at":"2025-06-20T12:54:09.858476Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 033042. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"033042","expires_at":"2025-06-20T12:54:09.858681Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 954220. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"954220","expires_at":"2025-06-20T12:54:09.869357Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 197816. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"197816","expires_at":"2025-06-20T12:54:09.874599Z"} 
[2025-06-20 12:39:09] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 732508. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"732508","expires_at":"2025-06-20T12:54:09.880082Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 760098. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"760098","expires_at":"2025-06-20T12:54:09.880287Z"} 
[2025-06-20 12:39:09] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 12:39:09] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 352897. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"352897","expires_at":"2025-06-20T12:54:09.885582Z"} 
[2025-06-20 12:39:09] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 684207. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"684207","expires_at":"2025-06-20T12:54:09.890908Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 025964. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"025964","expires_at":"2025-06-20T12:54:09.891627Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 113752. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"113752","expires_at":"2025-06-20T12:54:09.891869Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 574112. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"574112","expires_at":"2025-06-20T12:54:09.892071Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 812262. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"812262","expires_at":"2025-06-20T12:54:09.892320Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 129043. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"129043","expires_at":"2025-06-20T12:54:09.892553Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 085562. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"085562","expires_at":"2025-06-20T12:54:09.892748Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 857890. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"857890","expires_at":"2025-06-20T12:54:09.892935Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 665886. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"665886","expires_at":"2025-06-20T12:54:09.893123Z"} 
[2025-06-20 12:39:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 851802. This code will expire in 15 minutes."} 
[2025-06-20 12:39:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"851802","expires_at":"2025-06-20T12:54:09.893317Z"} 
[2025-06-20 12:39:11] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 107297. This code will expire in 15 minutes."} 
[2025-06-20 12:39:11] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"107297","expires_at":"2025-06-20T12:54:11.709852Z"} 
[2025-06-20 12:39:11] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:39:11] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 12:39:11] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 607317. This code will expire in 15 minutes."} 
[2025-06-20 12:39:11] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"607317","expires_at":"2025-06-20T12:54:11.749560Z"} 
[2025-06-20 12:39:11] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 12:39:11] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 940106. This code will expire in 15 minutes."} 
[2025-06-20 12:39:11] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"940106","expires_at":"2025-06-20T12:54:11.934815Z"} 
[2025-06-20 12:39:11] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 602248. This code will expire in 15 minutes."} 
[2025-06-20 12:39:11] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"602248","expires_at":"2025-06-20T12:54:11.972790Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 256703. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"256703","expires_at":"2025-06-20T14:25:07.732922Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 075760. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"075760","expires_at":"2025-06-20T14:25:07.740939Z"} 
[2025-06-20 14:10:07] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 648679. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"648679","expires_at":"2025-06-20T14:25:07.748501Z"} 
[2025-06-20 14:10:07] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 14:10:07] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 532582. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"532582","expires_at":"2025-06-20T14:25:07.765063Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 344303. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"344303","expires_at":"2025-06-20T14:25:07.765288Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 390754. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"390754","expires_at":"2025-06-20T14:25:07.775683Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 878639. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"878639","expires_at":"2025-06-20T14:25:07.780591Z"} 
[2025-06-20 14:10:07] testing.WARNING: Verification code resend blocked due to retry interval {"email":"<EMAIL>"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 224120. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"224120","expires_at":"2025-06-20T14:25:07.786864Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 055867. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"055867","expires_at":"2025-06-20T14:25:07.787075Z"} 
[2025-06-20 14:10:07] testing.WARNING: Invalid verification code provided {"email":"<EMAIL>"} 
[2025-06-20 14:10:07] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 099956. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"099956","expires_at":"2025-06-20T14:25:07.792277Z"} 
[2025-06-20 14:10:07] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 013265. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"013265","expires_at":"2025-06-20T14:25:07.797516Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 636592. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"636592","expires_at":"2025-06-20T14:25:07.799476Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 275860. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"275860","expires_at":"2025-06-20T14:25:07.799700Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 455612. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"455612","expires_at":"2025-06-20T14:25:07.799900Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 836758. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"836758","expires_at":"2025-06-20T14:25:07.800091Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 195810. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"195810","expires_at":"2025-06-20T14:25:07.800277Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 466728. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"466728","expires_at":"2025-06-20T14:25:07.800469Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 782093. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"782093","expires_at":"2025-06-20T14:25:07.800658Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 071427. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"071427","expires_at":"2025-06-20T14:25:07.800845Z"} 
[2025-06-20 14:10:07] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 435268. This code will expire in 15 minutes."} 
[2025-06-20 14:10:07] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"435268","expires_at":"2025-06-20T14:25:07.801029Z"} 
[2025-06-20 14:10:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 023905. This code will expire in 15 minutes."} 
[2025-06-20 14:10:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"023905","expires_at":"2025-06-20T14:25:09.591621Z"} 
[2025-06-20 14:10:09] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 14:10:09] testing.WARNING: Verification code not found or expired {"email":"<EMAIL>"} 
[2025-06-20 14:10:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 761807. This code will expire in 15 minutes."} 
[2025-06-20 14:10:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"761807","expires_at":"2025-06-20T14:25:09.623645Z"} 
[2025-06-20 14:10:09] testing.INFO: Verification code verified successfully {"email":"<EMAIL>"} 
[2025-06-20 14:10:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 735642. This code will expire in 15 minutes."} 
[2025-06-20 14:10:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"735642","expires_at":"2025-06-20T14:25:09.807076Z"} 
[2025-06-20 14:10:09] testing.INFO: Mock email sent {"to":"<EMAIL>","subject":"Email Verification Code","body":"Your verification code is: 373528. This code will expire in 15 minutes."} 
[2025-06-20 14:10:09] testing.INFO: Verification code sent {"email":"<EMAIL>","code":"373528","expires_at":"2025-06-20T14:25:09.841712Z"} 
